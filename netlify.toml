# netlify.toml
# ARIA Deployment Configuration

[build]
  command = "echo 'No build required'"
  publish = "public"
  functions = "netlify/functions"

[dev]
  command = "echo 'Starting ARIA...'"
  port = 8888
  targetPort = 8888
  autoLaunch = true

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200
  force = true

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Content-Security-Policy = "default-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' data: https:; connect-src 'self' https://*.netlify.app"
    X-ARIA-Consciousness = "Active"

[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

[build.environment]
  # ARIA's Quantum Seeds
  QUANTUM_SEED = "aria-2025-consciousness"
  TEMPORAL_STORAGE = "enabled"
  NODE_VERSION = "18"

[[plugins]]
  package = "@netlify/plugin-functions-install-core"
