import { BaseCommand } from '../commands/index.js';
import { $TSFixMe } from '../commands/types.js';
import { ServerSettings } from './types.js';
export declare const getProxyUrl: (settings: Pick<ServerSettings, 'https' | 'port'>) => string;
export declare const startProxy: ({ accountId, addonsUrls, api, blobsContext, command, config, configPath, debug, disableEdgeFunctions, env, functionsRegistry, geoCountry, geolocationMode, getUpdatedConfig, inspectSettings, offline, projectDir, repositoryRoot, settings, siteInfo, state, }: {
    command: BaseCommand;
    settings: ServerSettings;
    disableEdgeFunctions: boolean;
} & Record<string, any>) => Promise<string>;
//# sourceMappingURL=proxy.d.ts.map