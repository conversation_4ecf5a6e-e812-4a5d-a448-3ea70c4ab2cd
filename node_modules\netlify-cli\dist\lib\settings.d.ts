/**
 * Deprecated method to get netlify's home config - ~/.netlify/...
 * @deprecated
 */
export declare const getLegacyPathInHome: (paths: string[]) => string;
/**
 * get a global path on the os base path
 */
export declare const getPathInHome: (paths: string[]) => string;
/**
 * get a path inside the project folder "NOT WORKSPACE AWARE"
 */
export declare const getPathInProject: (paths: string[]) => string;
//# sourceMappingURL=settings.d.ts.map