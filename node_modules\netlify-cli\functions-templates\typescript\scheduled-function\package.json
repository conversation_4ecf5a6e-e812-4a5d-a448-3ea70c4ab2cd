{"name": "{{name}}", "version": "1.0.0", "description": "netlify functions:create - scheduled function in TypeScript", "main": "{{name}}.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["netlify", "serverless", "typescript", "schedule"], "author": "Netlify", "license": "MIT", "dependencies": {"@netlify/functions": "^2.8.2", "@types/node": "^22.0.0", "typescript": "^4.5.5"}}