import type { IncomingMessage } from 'http';
import BaseCommand from '../../commands/base-command.js';
import { $TSFixMe } from '../../commands/types.js';
import { BlobsContextWithEdgeAccess } from '../blobs/blobs.js';
declare const headersSymbol: unique symbol;
export declare const handleProxyRequest: (req: any, proxyReq: any) => void;
interface SiteInfo {
    id: string;
    name: string;
    url: string;
}
export declare const createSiteInfoHeader: (siteInfo: SiteInfo, localURL: string) => string;
export declare const createAccountInfoHeader: (accountInfo?: {}) => string;
export declare const initializeProxy: ({ accountId, blobsContext, command, config, configPath, debug, env: configEnv, geoCountry, geolocationMode, getUpdatedConfig, inspectSettings, mainPort, offline, passthroughPort, projectDir, repositoryRoot, settings, siteInfo, state, }: {
    accountId: string;
    blobsContext: BlobsContextWithEdgeAccess;
    command: BaseCommand;
    config: $TSFixMe;
    configPath: string;
    debug: boolean;
    env: $TSFixMe;
    offline: $TSFixMe;
    geoCountry: $TSFixMe;
    geolocationMode: $TSFixMe;
    getUpdatedConfig: $TSFixMe;
    inspectSettings: $TSFixMe;
    mainPort: $TSFixMe;
    passthroughPort: $TSFixMe;
    projectDir: string;
    repositoryRoot?: string | undefined;
    settings: $TSFixMe;
    siteInfo: $TSFixMe;
    state: $TSFixMe;
}) => Promise<(req: IncomingMessage & {
    [headersSymbol]: Record<string, string>;
}) => Promise<string | undefined>>;
export declare const isEdgeFunctionsRequest: (req: any) => boolean;
export {};
//# sourceMappingURL=proxy.d.ts.map