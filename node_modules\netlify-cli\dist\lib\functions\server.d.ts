import { type RequestHand<PERSON> } from 'express';
import type BaseCommand from '../../commands/base-command.js';
import type { $TSFixMe } from '../../commands/types.js';
import type { BlobsContext } from '../blobs/blobs.js';
import { FunctionsRegistry } from './registry.js';
export declare const createHandler: (options: GetFunctionsServerOptions) => RequestHandler;
interface GetFunctionsServerOptions {
    functionsRegistry: FunctionsRegistry;
    siteUrl: string;
    siteInfo?: $TSFixMe;
    accountId: string;
    geoCountry: string;
    offline: boolean;
    state: $TSFixMe;
    config: $TSFixMe;
    geolocationMode: 'cache' | 'update' | 'mock';
}
export declare const startFunctionsServer: (options: {
    blobsContext: BlobsContext;
    command: BaseCommand;
    config: $TSFixMe;
    capabilities: $TSFixMe;
    debug: boolean;
    loadDistFunctions?: boolean;
    settings: $TSFixMe;
    site: $TSFixMe;
    siteInfo: $TSFixMe;
    timeouts: $TSFixMe;
} & Omit<GetFunctionsServerOptions, 'functionsRegistry'>) => Promise<FunctionsRegistry | undefined>;
export {};
//# sourceMappingURL=server.d.ts.map