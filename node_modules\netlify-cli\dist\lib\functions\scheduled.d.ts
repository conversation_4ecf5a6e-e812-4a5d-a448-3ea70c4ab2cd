export declare const buildHelpResponse: ({ error, headers, path, result }: {
    error: any;
    headers: any;
    path: any;
    result: any;
}) => {
    statusCode: number;
    contentType: string;
    message: string;
};
export declare const handleScheduledFunction: ({ error, request, response, result }: {
    error: any;
    request: any;
    response: any;
    result: any;
}) => void;
//# sourceMappingURL=scheduled.d.ts.map