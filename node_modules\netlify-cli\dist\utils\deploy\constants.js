// Local deploy timeout in ms: 20 mins
export const DEFAULT_DEPLOY_TIMEOUT = 1200000;
// Concurrent file hash calls
export const DEFAULT_CONCURRENT_HASH = 100;
// Number of concurrent uploads
export const DEFAULT_CONCURRENT_UPLOAD = 5;
// Number of files
export const DEFAULT_SYNC_LIMIT = 100;
// Number of times to retry an upload
export const DEFAULT_MAX_RETRY = 5;
export const UPLOAD_RANDOM_FACTOR = 0.5;
// 5 seconds
export const UPLOAD_INITIAL_DELAY = 5000;
// 1.5 minute (90s)
export const UPLOAD_MAX_DELAY = 90000;
// 1 second
export const DEPLOY_POLL = 1000;
