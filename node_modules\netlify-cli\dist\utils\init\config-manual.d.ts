/**
 * @param {object} config
 * @param {import('../../commands/base-command.js').default} config.command
 * @param {Awaited<ReturnType<import('../../utils/get-repo-data.js').default>>} config.repoData
 * @param {string} config.siteId
 */
export default function configManual({ command, repoData, siteId }: {
    command: any;
    repoData: any;
    siteId: any;
}): Promise<void>;
//# sourceMappingURL=config-manual.d.ts.map