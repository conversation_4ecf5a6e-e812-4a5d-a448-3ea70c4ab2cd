import { Rewriter } from './types.js';
export declare const onChanges: (files: any, listener: any) => void;
export declare const getWatchers: () => any[];
export declare const getLanguage: (headers: any) => any;
export declare const createRewriter: ({ config, configPath, distDir, geoCountry, jwtRoleClaim, jwtSecret, projectDir, }: {
    config: any;
    configPath: any;
    distDir: any;
    geoCountry: any;
    jwtRoleClaim: any;
    jwtSecret: any;
    projectDir: any;
}) => Promise<Rewriter>;
//# sourceMappingURL=rules-proxy.d.ts.map