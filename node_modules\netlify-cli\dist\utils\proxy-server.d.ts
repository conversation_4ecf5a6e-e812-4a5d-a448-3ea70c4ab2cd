import BaseCommand from '../commands/base-command.js';
import { $TSFixMe, NetlifyOptions } from '../commands/types.js';
import { BlobsContextWithEdgeAccess } from '../lib/blobs/blobs.js';
import { FunctionsRegistry } from '../lib/functions/registry.js';
import type StateConfig from './state-config.js';
import { ServerSettings } from './types.js';
interface InspectSettings {
    enabled: boolean;
    pause: boolean;
    address?: string;
}
export declare const generateInspectSettings: (edgeInspect: boolean | string, edgeInspectBrk: boolean | string) => InspectSettings;
export declare const startProxyServer: ({ accountId, addonsUrls, api, blobsContext, command, config, configPath, debug, disableEdgeFunctions, env, functionsRegistry, geoCountry, geolocationMode, getUpdatedConfig, inspectSettings, offline, projectDir, repositoryRoot, settings, site, siteInfo, state, }: {
    accountId: string;
    addonsUrls: $TSFixMe;
    api?: (import("netlify").NetlifyAPI & Record<string, (...args: any) => Promise<any>>) | undefined;
    blobsContext?: BlobsContextWithEdgeAccess | undefined;
    command: BaseCommand;
    config: NetlifyOptions['config'];
    configPath?: string | undefined;
    debug: boolean;
    disableEdgeFunctions: boolean;
    env: NetlifyOptions['cachedConfig']['env'];
    inspectSettings: InspectSettings;
    getUpdatedConfig: () => Promise<object>;
    geolocationMode: string;
    geoCountry: string;
    settings: ServerSettings;
    offline: boolean;
    site: $TSFixMe;
    siteInfo: $TSFixMe;
    projectDir: string;
    repositoryRoot?: string | undefined;
    state: StateConfig;
    functionsRegistry?: FunctionsRegistry | undefined;
}) => Promise<string>;
export {};
//# sourceMappingURL=proxy-server.d.ts.map