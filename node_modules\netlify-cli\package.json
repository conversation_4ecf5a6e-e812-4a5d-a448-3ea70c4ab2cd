{"name": "netlify-cli", "description": "Netlify command line tool", "version": "17.38.1", "author": "Netlify Inc.", "type": "module", "engines": {"node": ">=18.14.0"}, "files": ["/bin", "/npm-shrinkwrap.json", "/scripts", "/functions-templates", "/dist"], "homepage": "https://github.com/netlify/cli", "keywords": ["api", "cli", "netlify", "static"], "license": "MIT", "repository": "https://github.com/netlify/cli", "bin": {"ntl": "./bin/run.js", "netlify": "./bin/run.js"}, "bugs": {"url": "https://github.com/netlify/cli/issues"}, "scripts": {"prepublishOnly": "node ./scripts/prepare-for-publish.js", "postinstall": "node ./scripts/postinstall.js"}, "dependencies": {"@bugsnag/js": "7.25.0", "@fastify/static": "7.0.4", "@netlify/blobs": "8.1.0", "@netlify/build": "29.58.0", "@netlify/build-info": "7.17.0", "@netlify/config": "20.21.0", "@netlify/edge-bundler": "12.3.1", "@netlify/edge-functions": "2.11.1", "@netlify/headers-parser": "7.3.0", "@netlify/local-functions-proxy": "1.1.1", "@netlify/redirect-parser": "14.5.0", "@netlify/zip-it-and-ship-it": "9.42.1", "@octokit/rest": "20.1.1", "@opentelemetry/api": "1.8.0", "ansi-escapes": "7.0.0", "ansi-styles": "6.2.1", "ansi-to-html": "0.7.2", "ascii-table": "0.0.9", "backoff": "2.5.0", "better-opn": "3.0.2", "boxen": "7.1.1", "chalk": "5.3.0", "chokidar": "3.6.0", "ci-info": "4.1.0", "clean-deep": "3.4.0", "commander": "10.0.1", "comment-json": "4.2.5", "concordance": "5.0.4", "configstore": "6.0.0", "content-type": "1.0.5", "cookie": "0.7.2", "cron-parser": "4.9.0", "debug": "4.3.7", "decache": "4.6.2", "dot-prop": "9.0.0", "dotenv": "16.4.7", "env-paths": "3.0.0", "envinfo": "7.14.0", "etag": "1.8.1", "execa": "5.1.1", "express": "4.21.2", "express-logging": "1.1.1", "extract-zip": "2.0.1", "fastest-levenshtein": "1.0.16", "fastify": "4.28.1", "find-up": "7.0.0", "flush-write-stream": "2.0.0", "folder-walker": "3.2.0", "from2-array": "0.0.4", "fuzzy": "0.1.3", "get-port": "5.1.1", "gh-release-fetch": "4.0.3", "git-repo-info": "2.1.1", "gitconfiglocal": "2.1.0", "hasbin": "1.2.3", "hasha": "5.2.2", "http-proxy": "1.18.1", "http-proxy-middleware": "2.0.7", "https-proxy-agent": "7.0.5", "inquirer": "6.5.2", "inquirer-autocomplete-prompt": "1.4.0", "ipx": "2.1.0", "is-docker": "3.0.0", "is-stream": "4.0.1", "is-wsl": "3.1.0", "isexe": "3.1.1", "js-yaml": "4.1.0", "jsonwebtoken": "9.0.2", "jwt-decode": "4.0.0", "lambda-local": "2.2.0", "listr2": "8.2.5", "locate-path": "7.2.0", "lodash": "4.17.21", "log-symbols": "6.0.0", "log-update": "6.1.0", "maxstache": "1.0.7", "maxstache-stream": "1.0.4", "multiparty": "4.2.3", "netlify": "13.2.0", "netlify-redirector": "0.5.0", "node-fetch": "3.3.2", "node-version-alias": "3.4.1", "ora": "8.1.1", "p-filter": "4.1.0", "p-map": "7.0.2", "p-wait-for": "5.0.2", "parallel-transform": "1.2.0", "parse-github-url": "1.0.3", "parse-gitignore": "2.0.0", "path-key": "4.0.0", "prettyjson": "1.2.5", "pump": "3.0.2", "raw-body": "2.5.2", "read-package-up": "11.0.0", "readdirp": "3.6.0", "semver": "7.6.3", "source-map-support": "0.5.21", "strip-ansi-control-characters": "2.0.0", "tabtab": "3.0.2", "tempy": "3.1.0", "terminal-link": "3.0.0", "through2-filter": "4.0.0", "through2-map": "4.0.0", "toml": "3.0.0", "tomlify-j0.4": "3.0.0", "ulid": "2.3.0", "unixify": "1.0.0", "update-notifier": "7.3.1", "uuid": "9.0.1", "wait-port": "1.1.0", "write-file-atomic": "5.0.1", "ws": "8.18.0", "zod": "3.23.8"}}