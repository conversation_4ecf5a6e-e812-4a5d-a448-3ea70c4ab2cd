/**
 * @param {object} config
 * @param {import('../../commands/base-command.js').default} config.command
 * @param {boolean} config.manual
 * @param {*} config.repoData
 * @param {string} config.siteId
 */
export declare const configureRepo: ({ command, manual, repoData, siteId }: {
    command: any;
    manual: any;
    repoData: any;
    siteId: any;
}) => Promise<void>;
//# sourceMappingURL=config.d.ts.map