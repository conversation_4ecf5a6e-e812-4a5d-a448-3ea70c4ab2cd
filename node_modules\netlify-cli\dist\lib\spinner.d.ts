import { Ora } from 'ora';
/**
 * Creates a spinner with the following text
 */
export declare const startSpinner: ({ text }: {
    text: string;
}) => Ora;
/**
 * Stops the spinner with the following text
 */
export declare const stopSpinner: ({ error, spinner, text }: {
    error: boolean;
    spinner: Ora;
    text?: string | undefined;
}) => void;
/**
 * Clears the spinner
 */
export declare const clearSpinner: ({ spinner }: {
    spinner: Ora;
}) => void;
//# sourceMappingURL=spinner.d.ts.map