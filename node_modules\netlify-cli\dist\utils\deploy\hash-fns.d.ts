import BaseCommand from '../../commands/base-command.js';
import { $TSFixMe } from '../../commands/types.js';
declare const hashFns: (command: BaseCommand, directories: string[], config: {
    /** @default 'function' */
    assetType?: string;
    concurrentHash?: number;
    functionsConfig: $TSFixMe;
    /** @default 'sha256' */
    hashAlgorithm?: string;
    manifestPath: $TSFixMe;
    rootDir: $TSFixMe;
    skipFunctionsCache: $TSFixMe;
    statusCb: $TSFixMe;
    tmpDir: $TSFixMe;
}) => Promise<$TSFixMe>;
export default hashFns;
//# sourceMappingURL=hash-fns.d.ts.map