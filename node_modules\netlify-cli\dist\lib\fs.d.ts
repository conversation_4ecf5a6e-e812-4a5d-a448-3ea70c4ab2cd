export declare const fileExistsAsync: (filePath: any) => Promise<boolean>;
/**
 * Checks if the provided filePath is a file
 * @param {string} filePath
 */
export declare const isFileAsync: (filePath: any) => Promise<any>;
/**
 * Checks if the provided filePath is a directory
 * @param {string} filePath
 */
export declare const isDirectoryAsync: (filePath: any) => Promise<any>;
//# sourceMappingURL=fs.d.ts.map