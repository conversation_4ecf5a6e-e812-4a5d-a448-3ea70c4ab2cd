import logSymbols from 'log-symbols';
import ora from 'ora';
/**
 * Creates a spinner with the following text
 */
export const startSpinner = ({ text }) => ora({
    text,
}).start();
/**
 * Stops the spinner with the following text
 */
export const stopSpinner = ({ error, spinner, text }) => {
    if (!spinner) {
        return;
    }
    // TODO: refactor no package needed `log-symbols` for that
    const symbol = error ? logSymbols.error : logSymbols.success;
    spinner.stopAndPersist({
        text,
        symbol,
    });
};
/**
 * Clears the spinner
 */
export const clearSpinner = ({ spinner }) => {
    if (spinner) {
        spinner.stop();
    }
};
