export declare const concordanceOptions: {
    maxDepth: number;
    plugins: any[];
    theme: {
        boolean: import("ansi-styles").CSPair;
        circular: string;
        date: {
            invalid: string;
            value: import("ansi-styles").CSPair;
        };
        diffGutters: {
            actual: string;
            expected: string;
            padding: string;
        };
        error: {
            ctor: {
                open: string;
                close: string;
            };
            name: import("ansi-styles").CSPair;
        };
        function: {
            name: import("ansi-styles").CSPair;
            stringTag: import("ansi-styles").CSPair;
        };
        global: import("ansi-styles").CSPair;
        item: {
            after: string;
        };
        list: {
            openBracket: string;
            closeBracket: string;
        };
        mapEntry: {
            after: string;
        };
        maxDepth: string;
        null: import("ansi-styles").CSPair;
        number: import("ansi-styles").CSPair;
        object: {
            openBracket: string;
            closeBracket: string;
            ctor: import("ansi-styles").CSPair;
            stringTag: {
                open: string;
                close: string;
            };
            secondaryStringTag: {
                open: string;
                close: string;
            };
        };
        property: {
            after: string;
            keyBracket: {
                open: string;
                close: string;
            };
            valueFallback: string;
        };
        regexp: {
            source: {
                open: string;
                close: string;
            };
            flags: import("ansi-styles").CSPair;
        };
        stats: {
            separator: string;
        };
        string: {
            open: string;
            close: string;
            line: {
                open: string;
                close: string;
            };
            multiline: {
                start: string;
                end: string;
            };
            controlPicture: import("ansi-styles").CSPair;
            diff: {
                insert: {
                    open: string;
                    close: string;
                };
                delete: {
                    open: string;
                    close: string;
                };
                equal: import("ansi-styles").CSPair;
                insertLine: {
                    open: string;
                    close: string;
                };
                deleteLine: {
                    open: string;
                    close: string;
                };
            };
        };
        symbol: import("ansi-styles").CSPair;
        typedArray: {
            bytes: import("ansi-styles").CSPair;
        };
        undefined: import("ansi-styles").CSPair;
    };
};
export declare const concordanceDiffOptions: {
    maxDepth: number;
    plugins: any[];
    theme: {
        boolean: import("ansi-styles").CSPair;
        circular: string;
        date: {
            invalid: string;
            value: import("ansi-styles").CSPair;
        };
        diffGutters: {
            actual: string;
            expected: string;
            padding: string;
        };
        error: {
            ctor: {
                open: string;
                close: string;
            };
            name: import("ansi-styles").CSPair;
        };
        function: {
            name: import("ansi-styles").CSPair;
            stringTag: import("ansi-styles").CSPair;
        };
        global: import("ansi-styles").CSPair;
        item: {
            after: string;
        };
        list: {
            openBracket: string;
            closeBracket: string;
        };
        mapEntry: {
            after: string;
        };
        maxDepth: string;
        null: import("ansi-styles").CSPair;
        number: import("ansi-styles").CSPair;
        object: {
            openBracket: string;
            closeBracket: string;
            ctor: import("ansi-styles").CSPair;
            stringTag: {
                open: string;
                close: string;
            };
            secondaryStringTag: {
                open: string;
                close: string;
            };
        };
        property: {
            after: string;
            keyBracket: {
                open: string;
                close: string;
            };
            valueFallback: string;
        };
        regexp: {
            source: {
                open: string;
                close: string;
            };
            flags: import("ansi-styles").CSPair;
        };
        stats: {
            separator: string;
        };
        string: {
            open: string;
            close: string;
            line: {
                open: string;
                close: string;
            };
            multiline: {
                start: string;
                end: string;
            };
            controlPicture: import("ansi-styles").CSPair;
            diff: {
                insert: {
                    open: string;
                    close: string;
                };
                delete: {
                    open: string;
                    close: string;
                };
                equal: import("ansi-styles").CSPair;
                insertLine: {
                    open: string;
                    close: string;
                };
                deleteLine: {
                    open: string;
                    close: string;
                };
            };
        };
        symbol: import("ansi-styles").CSPair;
        typedArray: {
            bytes: import("ansi-styles").CSPair;
        };
        undefined: import("ansi-styles").CSPair;
    };
};
//# sourceMappingURL=options.d.ts.map