export declare const hasherCtor: ({ concurrentHash, hashAlgorithm }: {
    concurrentHash: any;
    hashAlgorithm: any;
}) => any;
export declare const fileNormalizerCtor: ({ assetType, normalizer: normalizeFunction }: {
    assetType: any;
    normalizer: any;
}) => any;
export declare const manifestCollectorCtor: (filesObj: any, shaMap: any, { assetType, statusCb }: {
    assetType: any;
    statusCb: any;
}) => any;
export declare const fileFilterCtor: any;
//# sourceMappingURL=hasher-segments.d.ts.map