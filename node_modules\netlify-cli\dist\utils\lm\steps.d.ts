export declare const checkGitVersionStep: {
    title: string;
    task: (ctx: any, task: any) => Promise<void>;
};
export declare const checkGitLFSVersionStep: {
    title: string;
    task: (ctx: any, task: any) => Promise<void>;
};
export declare const checkLFSFiltersStep: (onCheckDone: any) => {
    title: string;
    task: (ctx: any, task: any) => Promise<any>;
};
export declare const checkHelperVersionStep: {
    title: string;
    task: (ctx: any, task: any) => Promise<void>;
};
//# sourceMappingURL=steps.d.ts.map